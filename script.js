// DOM Elements
const giftSection = document.querySelector('.gift-section');
const confettiContainer = document.getElementById('confetti-container');
const audio = document.getElementById('birthday-music');
const musicToggle = document.getElementById('music-toggle');
let isMusicPlaying = false;

// Confetti Colors
const confettiColors = [
    '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57',
    '#ff9ff3', '#54a0ff', '#5f27cd', '#00d2d3', '#ff9f43'
];

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Add loading animation
    document.body.classList.add('loading');
    
    // Add click event to gift box
    giftSection.addEventListener('click', openGift);
    
    // Auto confetti on load
    setTimeout(() => {
        throwConfetti();
    }, 2000);
    
    // Add some interactive elements
    addInteractiveElements();
    
    // Initialize fade-in animations
    initFadeInAnimations();
    
    // Create stars background
    createStars();
    
    // Animate sticker and balloons
    animateSticker();
    animateBalloons();
});

// Initialize fade-in animations with delays
function initFadeInAnimations() {
    const fadeElements = document.querySelectorAll('.fade-in-text');
    
    fadeElements.forEach(element => {
        const delay = parseFloat(element.getAttribute('data-delay')) || 0;
        
        setTimeout(() => {
            element.style.animationDelay = '0s';
            element.style.animation = 'fadeInUp 0.8s ease-out forwards';
        }, delay * 1000);
    });
}

// Tạo ngôi sao lấp lánh
function createStars() {
    const starsBg = document.getElementById('stars-bg');
    const starCount = 50;
    
    for (let i = 0; i < starCount; i++) {
        const star = document.createElement('div');
        star.className = 'star';
        star.style.left = Math.random() * 100 + '%';
        star.style.top = Math.random() * 100 + '%';
        star.style.animationDelay = Math.random() * 2 + 's';
        starsBg.appendChild(star);
    }
}

// Tạo trái tim bay
function createHearts() {
    const hearts = document.querySelectorAll('.heart');
    hearts.forEach((heart, index) => {
        heart.style.left = Math.random() * 100 + '%';
        heart.style.animationDelay = Math.random() * 3 + 's';
        heart.style.animationDuration = (Math.random() * 3 + 2) + 's';
    });
}

// Hiệu ứng mở thư - Smooth and optimized
function openLetter() {
    // Hiển thị letter modal trực tiếp với hiệu ứng mượt mà
    showLetterModal();
}

// Hiển thị letter modal với smooth animations
function showLetterModal() {
    const modal = document.createElement('div');
    modal.className = 'letter-modal';
    modal.innerHTML = `
        <div class="letter-container">
            <div class="letter-paper">
                <div class="letter-header">
                    <div class="letter-title">💌 Chúc Mừng Sinh Nhật</div>
                </div>

                <div class="letter-content">
                    <div class="typewriter-text enhanced" id="letterText"></div>
                    <div class="content-sparkles">
                        <div class="sparkle sparkle-1">✨</div>
                        <div class="sparkle sparkle-2">⭐</div>
                        <div class="sparkle sparkle-3">💫</div>
                    </div>
                </div>

                <div class="letter-signature">
                    <div class="signature-line">Với tất cả tình yêu thương ❤️</div>
                    <div class="signature-name">Em trai</div>
                </div>

                <div class="letter-decorations">
                    <div class="decoration-heart">💖</div>
                    <div class="decoration-star">✨</div>
                    <div class="decoration-flower">🌸</div>
                </div>
            </div>

            <button class="letter-close-btn" onclick="closeLetter()">✕</button>
        </div>
    `;

    document.body.appendChild(modal);

    // Smooth entrance animation
    setTimeout(() => {
        startSmoothTypewriter();
        createSimpleFloatingHearts();
        enhanceLetterVisuals();
    }, 400);

    // Thêm hiệu ứng confetti nhẹ
    createConfetti(15);
}

// Removed complex particle effects for better performance

// Smooth typewriter effect - optimized for performance
function startSmoothTypewriter() {
    const fullText = `Chúc mừng sinh nhật chị gái yêu quý!
Chúc chị luôn rạng rỡ, xinh đẹp và tràn đầy sức sống.
Mỗi ngày trôi qua đều là một ngày vui vẻ, hạnh phúc và ngập tràn những điều tốt đẹp.
Mong rằng cuộc sống luôn mỉm cười dịu dàng với chị – vì chị xứng đáng với tất cả những điều tuyệt vời nhất trên đời.`;

    smoothTypeWriter('letterText', fullText, 60);
}

// Simple and smooth typewriter function
function smoothTypeWriter(elementId, text, speed, callback) {
    const element = document.getElementById(elementId);
    let i = 0;

    // Clear element and add cursor
    element.innerHTML = '<span class="typing-cursor">|</span>';

    function type() {
        if (i < text.length) {
            // Remove cursor temporarily
            element.innerHTML = element.innerHTML.replace('<span class="typing-cursor">|</span>', '');

            // Add character
            const char = text.charAt(i);
            if (char === '\n') {
                element.innerHTML += '<br>';
            } else {
                element.innerHTML += char;
            }

            // Add cursor back
            element.innerHTML += '<span class="typing-cursor">|</span>';

            i++;
            setTimeout(type, speed);
        } else {
            // Remove final cursor
            element.innerHTML = element.innerHTML.replace('<span class="typing-cursor">|</span>', '');

            // Add final signature animation
            setTimeout(() => {
                animateSignature();
            }, 300);

            if (callback) callback();
        }
    }

    // Start typing after a brief delay
    setTimeout(type, 200);
}

// Animate signature with handwritten effect
function animateSignature() {
    const signatureLine = document.querySelector('.signature-line');
    const signatureName = document.querySelector('.signature-name');

    if (signatureLine) {
        signatureLine.style.animation = 'handwrittenAppear 1s ease-out forwards';
    }

    if (signatureName) {
        setTimeout(() => {
            signatureName.style.animation = 'handwrittenAppear 1.2s ease-out forwards';
        }, 500);
    }
}

// Simple floating hearts - optimized for performance
function createSimpleFloatingHearts() {
    const modal = document.querySelector('.letter-modal');
    if (!modal) return;

    const heartEmojis = ['💕', '💖', '💗'];

    for (let i = 0; i < 4; i++) {
        setTimeout(() => {
            const heart = document.createElement('div');
            heart.className = 'simple-floating-heart';
            heart.innerHTML = heartEmojis[Math.floor(Math.random() * heartEmojis.length)];

            heart.style.cssText = `
                position: fixed;
                left: ${20 + Math.random() * 60}%;
                top: ${20 + Math.random() * 60}%;
                font-size: 1.5rem;
                animation: simpleHeartFloat 3s ease-in-out forwards;
                z-index: 999;
                pointer-events: none;
                opacity: 0.8;
            `;

            modal.appendChild(heart);

            setTimeout(() => heart.remove(), 3000);
        }, i * 500);
    }
}

// Legacy function for compatibility
function startSinglePageTypewriter() {
    startSmoothTypewriter();
}

// Legacy typewriter function for compatibility
function typeWriter(elementId, text, speed, callback) {
    smoothTypeWriter(elementId, text, speed, callback);
}

// Enhanced typewriter effect with handwritten style (legacy)
function startEnhancedTypewriter() {
    startSmoothTypewriter();
}

// Enhanced typewriter function (legacy)
function enhancedTypeWriter(elementId, text, speed, callback) {
    smoothTypeWriter(elementId, text, speed, callback);
}

// Create floating hearts (legacy)
function createFloatingHearts() {
    createSimpleFloatingHearts();
}

// Enhance letter visuals with subtle effects
function enhanceLetterVisuals() {
    const letterContent = document.querySelector('.letter-content');
    if (!letterContent) return;

    // Add gentle glow effect to the letter content
    setTimeout(() => {
        const typewriterText = document.querySelector('.typewriter-text');
        if (typewriterText) {
            typewriterText.style.animation = 'gentleContentGlow 6s ease-in-out infinite';
        }
    }, 2000);

    // Create subtle floating elements around the content
    createContentFloatingElements();
}

// Create subtle floating elements for letter content
function createContentFloatingElements() {
    const letterPaper = document.querySelector('.letter-paper');
    if (!letterPaper) return;

    const elements = ['✨', '💖', '🌸'];

    for (let i = 0; i < 3; i++) {
        setTimeout(() => {
            const element = document.createElement('div');
            element.className = 'content-floating-element';
            element.innerHTML = elements[i];

            const side = Math.random() > 0.5 ? 'left' : 'right';
            const topPosition = 30 + Math.random() * 40; // 30% to 70%

            element.style.cssText = `
                position: absolute;
                ${side}: -20px;
                top: ${topPosition}%;
                font-size: 1rem;
                opacity: 0.6;
                animation: contentElementFloat 8s ease-in-out infinite;
                animation-delay: ${i * 2}s;
                z-index: 2;
                pointer-events: none;
                color: #ff6b9d;
            `;

            letterPaper.appendChild(element);

            // Remove after animation cycle
            setTimeout(() => element.remove(), 8000);
        }, i * 1500);
    }
}

// Đóng thư
function closeLetter() {
    const modal = document.querySelector('.letter-modal');
    if (modal) {
        modal.style.animation = 'letterModalFadeOut 0.5s ease-out forwards';
        setTimeout(() => {
            modal.remove();
        }, 500);
    }
}

// Hiệu ứng mở hộp quà
function openGift() {
    // Hiệu ứng mở hộp quà
    const giftBox = document.querySelector('.gift-box');
    const giftLid = document.querySelector('.gift-lid');

    // Animation mở hộp
    giftLid.style.transform = 'translateX(-50%) translateY(-30px) rotate(-15deg)';
    giftBox.style.animation = 'giftOpen 1s ease-out forwards';

    // Thêm hiệu ứng glow
    giftBox.style.boxShadow = '0 0 30px rgba(255, 107, 107, 0.8)';

    // Tạo confetti
    createConfetti(50);

    // Thêm hiệu ứng ảnh bay ra từ hộp quà
    const giftImages = [
        'img/cute.png',
        'img/beautiful.png',
        'img/my_sister.png'
    ];
    for (let i = 0; i < 8; i++) {
        createFlyingImage(giftBox, giftImages[i % giftImages.length]);
    }
    // Thêm hiệu ứng icon nhỏ bay ra
    for (let i = 0; i < 8; i++) {
        createFlyingIcon(giftBox);
    }

    // Hiển thị ảnh trôi
    setTimeout(() => {
        showFloatingImages();
    }, 500);

    // Reset sau 3 giây
    setTimeout(() => {
        giftLid.style.transform = 'translateX(-50%)';
        giftBox.style.animation = '';
        giftBox.style.boxShadow = '';
    }, 3000);
}

// Birthday cake effect with bubbles and cherry blossoms
function openCakeEffect() {
    // Create background blur overlay
    const overlay = document.createElement('div');
    overlay.className = 'cake-effect-overlay';
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 182, 193, 0.3);
        backdrop-filter: blur(8px);
        z-index: 1000;
        animation: overlayFadeIn 0.5s ease-out;
        pointer-events: none;
    `;
    document.body.appendChild(overlay);

    // Create bubble container
    const bubbleContainer = document.createElement('div');
    bubbleContainer.className = 'bubble-effect-container';
    bubbleContainer.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 1001;
    `;
    document.body.appendChild(bubbleContainer);

    // Create close button with simpler approach
    const closeButton = document.createElement('button');
    closeButton.innerHTML = '✕';
    closeButton.onclick = function() {
        console.log('Close button clicked!');
        // Remove all effect elements immediately
        if (overlay) overlay.remove();
        if (bubbleContainer) bubbleContainer.remove();
        if (closeButton) closeButton.remove();

        // Clear intervals
        if (window.currentCakeEffect && window.currentCakeEffect.intervals) {
            window.currentCakeEffect.intervals.forEach(interval => clearInterval(interval));
        }

        // Reset cake container
        const cakeContainer = document.querySelector('.cake-container');
        if (cakeContainer) {
            cakeContainer.style.filter = '';
            cakeContainer.style.transform = '';
        }

        // Clear reference
        window.currentCakeEffect = null;

        console.log('Effect closed successfully');
    };

    closeButton.style.cssText = `
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 60px;
        height: 60px;
        background: #ff6b9d;
        border: 3px solid white;
        border-radius: 50%;
        color: white;
        font-size: 24px;
        font-weight: bold;
        cursor: pointer;
        z-index: 999999;
        box-shadow: 0 4px 15px rgba(255, 107, 157, 0.6);
        outline: none;
    `;

    document.body.appendChild(closeButton);

    // Available images
    const images = [
        'img/my_sister.png',
        'img/cute.png',
        'img/beautiful.png'
    ];

    // Start continuous image display effect
    startContinuousImageEffect(bubbleContainer, images);

    // Add escape key listener
    const escapeHandler = (e) => {
        if (e.key === 'Escape') {
            closeCakeEffect();
            document.removeEventListener('keydown', escapeHandler);
        }
    };
    document.addEventListener('keydown', escapeHandler);

    // Add cake container glow effect
    const cakeContainer = document.querySelector('.cake-container');
    cakeContainer.style.filter = 'drop-shadow(0 0 30px rgba(255, 182, 193, 0.8))';
    cakeContainer.style.transform = 'scale(1.2)';

    // Store references for cleanup
    window.currentCakeEffect = {
        overlay,
        bubbleContainer,
        closeButton,
        cakeContainer,
        intervals: []
    };
}

// Function to close cake effect (simplified)
function closeCakeEffect() {
    console.log('closeCakeEffect called');

    // Remove overlay
    const overlay = document.querySelector('.cake-effect-overlay');
    if (overlay) overlay.remove();

    // Remove bubble container
    const bubbleContainer = document.querySelector('.bubble-effect-container');
    if (bubbleContainer) bubbleContainer.remove();

    // Remove close button
    const closeButton = document.querySelector('button');
    if (closeButton && closeButton.innerHTML === '✕') {
        closeButton.remove();
    }

    // Clear intervals
    if (window.currentCakeEffect && window.currentCakeEffect.intervals) {
        window.currentCakeEffect.intervals.forEach(interval => clearInterval(interval));
    }

    // Reset cake container
    const cakeContainer = document.querySelector('.cake-container');
    if (cakeContainer) {
        cakeContainer.style.filter = '';
        cakeContainer.style.transform = '';
    }

    // Clear reference
    window.currentCakeEffect = null;

    console.log('Cake effect closed successfully');
}

// Create floating image bubbles for cake effect
function createImageBubble(container, imageSrc) {
    const bubble = document.createElement('div');
    bubble.className = 'image-bubble';

    const img = document.createElement('img');
    img.src = imageSrc;
    img.style.cssText = `
        width: 85%;
        height: 85%;
        object-fit: cover;
        border-radius: 50%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    `;

    bubble.appendChild(img);

    const size = Math.random() * 60 + 100; // 100-160px
    const startX = Math.random() * (window.innerWidth - size);
    const driftX = (Math.random() - 0.5) * 150; // Gentle horizontal drift
    const duration = Math.random() * 4 + 6; // 6-10 seconds

    bubble.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        border-radius: 50%;
        background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.6), rgba(255, 182, 193, 0.3));
        border: 2px solid rgba(255, 255, 255, 0.8);
        box-shadow:
            0 8px 32px rgba(255, 107, 157, 0.2),
            inset 0 2px 8px rgba(255, 255, 255, 0.6),
            inset 0 -2px 8px rgba(255, 182, 193, 0.3);
        left: ${startX}px;
        top: ${window.innerHeight + 50}px;
        animation: realisticBubbleFloat ${duration}s ease-out forwards;
        backdrop-filter: blur(1px);
        overflow: hidden;
        --drift-x: ${driftX}px;
    `;

    // Add bubble shine effect
    const shine = document.createElement('div');
    shine.style.cssText = `
        position: absolute;
        top: 15%;
        left: 25%;
        width: 30%;
        height: 30%;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
        border-radius: 50%;
        pointer-events: none;
    `;
    bubble.appendChild(shine);

    container.appendChild(bubble);

    // Remove bubble after animation
    setTimeout(() => {
        if (bubble.parentNode) {
            bubble.remove();
        }
    }, duration * 1000);
}

// Create cherry blossom petals
function createCherryBlossomPetal(container) {
    const petal = document.createElement('div');
    petal.innerHTML = '🌸';

    const startX = Math.random() * window.innerWidth;
    const drift = (Math.random() - 0.5) * 400; // More realistic drift
    const duration = Math.random() * 4 + 6; // 6-10 seconds
    const size = Math.random() * 1.5 + 1.5; // 1.5-3rem
    const rotation = Math.random() * 720 + 360; // 360-1080 degrees

    petal.style.cssText = `
        position: absolute;
        font-size: ${size}rem;
        left: ${startX}px;
        top: -50px;
        animation: petalFall ${duration}s ease-in forwards;
        pointer-events: none;
        z-index: 1002;
        --drift: ${drift}px;
        --rotation: ${rotation}deg;
        filter: drop-shadow(0 2px 4px rgba(255, 182, 193, 0.3));
    `;

    // Add custom animation with realistic physics
    petal.style.setProperty('--drift', drift + 'px');

    container.appendChild(petal);

    // Remove petal after animation
    setTimeout(() => {
        if (petal.parentNode) {
            petal.remove();
        }
    }, duration * 1000);
}

// Start continuous image display effect with faster speed
function startContinuousImageEffect(container, images) {
    // Create initial images more frequently
    for (let i = 0; i < 8; i++) {
        setTimeout(() => {
            createRandomCircularImage(container, images);
        }, i * 150);
    }

    // Set interval for continuous image display every 400ms (much faster)
    const imageInterval = setInterval(() => {
        if (document.querySelector('.bubble-effect-container')) {
            createRandomCircularImage(container, images);
            // Add sparkle effects
            createSparkleEffect(container);
        } else {
            clearInterval(imageInterval);
        }
    }, 400);

    // Add floating hearts effect
    const heartInterval = setInterval(() => {
        if (document.querySelector('.bubble-effect-container')) {
            createFloatingHeart(container);
        } else {
            clearInterval(heartInterval);
        }
    }, 800);

    // Store intervals for cleanup
    if (window.currentCakeEffect && window.currentCakeEffect.intervals) {
        window.currentCakeEffect.intervals.push(imageInterval);
        window.currentCakeEffect.intervals.push(heartInterval);
    }
}

// Create circular image at random position with 3D effects
function createRandomCircularImage(container, images) {
    const imageElement = document.createElement('div');
    imageElement.className = 'floating-circular-image-3d';

    const img = document.createElement('img');
    img.src = images[Math.floor(Math.random() * images.length)];
    img.style.cssText = `
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
        transition: transform 0.3s ease;
    `;

    imageElement.appendChild(img);

    const size = Math.random() * 50 + 70; // 70-120px
    const startX = Math.random() * (window.innerWidth - size);
    const startY = Math.random() * (window.innerHeight - size);
    const rotationX = Math.random() * 360;
    const rotationY = Math.random() * 360;

    imageElement.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        border-radius: 50%;
        border: 4px solid rgba(255, 182, 193, 0.9);
        box-shadow:
            0 8px 25px rgba(255, 107, 157, 0.4),
            0 0 20px rgba(255, 255, 255, 0.3),
            inset 0 2px 10px rgba(255, 255, 255, 0.2);
        left: ${startX}px;
        top: ${startY}px;
        animation: image3DFloat 4s ease-out forwards;
        overflow: hidden;
        opacity: 0;
        transform-style: preserve-3d;
        transform: perspective(1000px) rotateX(${rotationX}deg) rotateY(${rotationY}deg);
        backdrop-filter: blur(1px);
    `;

    // Add glow effect
    const glow = document.createElement('div');
    glow.style.cssText = `
        position: absolute;
        top: -10px;
        left: -10px;
        right: -10px;
        bottom: -10px;
        border-radius: 50%;
        background: radial-gradient(circle, rgba(255, 182, 193, 0.3) 0%, transparent 70%);
        animation: glowPulse 2s ease-in-out infinite;
        z-index: -1;
    `;
    imageElement.appendChild(glow);

    container.appendChild(imageElement);

    // Remove image after 4 seconds
    setTimeout(() => {
        if (imageElement.parentNode) {
            imageElement.remove();
        }
    }, 4000);
}

// Create sparkle effect
function createSparkleEffect(container) {
    const sparkle = document.createElement('div');
    sparkle.innerHTML = '✨';

    const startX = Math.random() * window.innerWidth;
    const startY = Math.random() * window.innerHeight;
    const size = Math.random() * 1.5 + 1; // 1-2.5rem

    sparkle.style.cssText = `
        position: absolute;
        font-size: ${size}rem;
        left: ${startX}px;
        top: ${startY}px;
        animation: sparkleFloat 3s ease-out forwards;
        pointer-events: none;
        z-index: 1003;
        filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.8));
    `;

    container.appendChild(sparkle);

    setTimeout(() => {
        if (sparkle.parentNode) {
            sparkle.remove();
        }
    }, 3000);
}

// Create floating heart effect
function createFloatingHeart(container) {
    const hearts = ['💖', '💕', '💝', '💗', '💓'];
    const heart = document.createElement('div');
    heart.innerHTML = hearts[Math.floor(Math.random() * hearts.length)];

    const startX = Math.random() * window.innerWidth;
    const startY = window.innerHeight + 50;
    const size = Math.random() * 1.5 + 1.5; // 1.5-3rem
    const drift = (Math.random() - 0.5) * 200;

    heart.style.cssText = `
        position: absolute;
        font-size: ${size}rem;
        left: ${startX}px;
        top: ${startY}px;
        animation: heartFloat 6s ease-out forwards;
        pointer-events: none;
        z-index: 1003;
        filter: drop-shadow(0 2px 8px rgba(255, 107, 157, 0.4));
        --drift: ${drift}px;
    `;

    container.appendChild(heart);

    setTimeout(() => {
        if (heart.parentNode) {
            heart.remove();
        }
    }, 6000);
}

// Tạo hiệu ứng ảnh bay ra khi mở quà
function createFlyingImage(parent, src) {
    const img = document.createElement('img');
    img.src = src;
    img.className = 'flying-gift-img';
    img.style.position = 'absolute';
    img.style.left = '50%';
    img.style.top = '50%';
    img.style.transform = 'translate(-50%, -50%) scale(0.7)';
    img.style.width = (Math.random() * 40 + 60) + 'px';
    img.style.height = 'auto';
    img.style.opacity = 1;
    img.style.borderRadius = '18px';
    img.style.boxShadow = '0 4px 16px rgba(255, 122, 193, 0.25)';
    img.style.pointerEvents = 'none';
    img.style.zIndex = 21;
    parent.appendChild(img);
    setTimeout(() => {
        img.style.transition = 'all 1.3s cubic-bezier(.68,-0.55,.27,1.55)';
        img.style.transform = `translate(${Math.random()*220-110}px, ${-Math.random()*160-80}px) scale(${Math.random()*0.5+0.8}) rotate(${Math.random()*60-30}deg)`;
        img.style.opacity = 0;
    }, 30);
    setTimeout(() => {
        img.remove();
    }, 1400);
}

// Tạo icon nhỏ bay ra khi mở quà
function createFlyingIcon(parent) {
    const icons = ['💖', '✨', '🌸', '🎉', '🌟', '🎈'];
    const icon = document.createElement('div');
    icon.className = 'flying-icon';
    icon.innerText = icons[Math.floor(Math.random() * icons.length)];
    icon.style.position = 'absolute';
    icon.style.left = '50%';
    icon.style.top = '50%';
    icon.style.transform = 'translate(-50%, -50%) scale(1)';
    icon.style.fontSize = (Math.random() * 18 + 22) + 'px';
    icon.style.opacity = 1;
    icon.style.pointerEvents = 'none';
    icon.style.zIndex = 20;
    parent.appendChild(icon);
    setTimeout(() => {
        icon.style.transition = 'all 1.2s cubic-bezier(.68,-0.55,.27,1.55)';
        icon.style.transform = `translate(${Math.random()*180-90}px, ${-Math.random()*120-60}px) scale(${Math.random()*0.5+0.8})`;
        icon.style.opacity = 0;
    }, 30);
    setTimeout(() => {
        icon.remove();
    }, 1300);
}

// Hiển thị ảnh trôi như bong bóng
function showFloatingImages() {
    const floatingImages = document.getElementById('floating-images');
    floatingImages.style.display = 'block';
    
    // Thêm hiệu ứng xuất hiện
    const images = floatingImages.querySelectorAll('.floating-img');
    images.forEach((img, index) => {
        img.style.opacity = '0';
        img.style.transform = 'scale(0)';
        
        setTimeout(() => {
            img.style.transition = 'all 0.8s ease-out';
            img.style.opacity = '0.9';
            img.style.transform = 'scale(1)';
        }, index * 300);
    });
    
    // Ẩn ảnh sau 12 giây
    setTimeout(() => {
        images.forEach((img, index) => {
            setTimeout(() => {
                img.style.opacity = '0';
                img.style.transform = 'scale(0)';
            }, index * 200);
        });
        
        setTimeout(() => {
            floatingImages.style.display = 'none';
        }, images.length * 200 + 500);
    }, 12000);
}

// Tạo confetti
function createConfetti(count) {
    const container = document.getElementById('confetti-container');
    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7', '#dda0dd', '#98d8c8'];
    
    for (let i = 0; i < count; i++) {
        const confetti = document.createElement('div');
        confetti.className = 'confetti';
        confetti.style.left = Math.random() * 100 + '%';
        confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
        confetti.style.animationDelay = Math.random() * 0.5 + 's';
        confetti.style.animationDuration = (Math.random() * 2 + 2) + 's';
        
        container.appendChild(confetti);
        
        // Xóa confetti sau khi animation kết thúc
        setTimeout(() => {
            if (confetti.parentNode) {
                confetti.remove();
            }
        }, 5000);
    }
}

// Thêm hiệu ứng sparkle cho ảnh
function addSparkleEffect() {
    const photoFrame = document.querySelector('.photo-frame');
    if (!photoFrame) return;
    
    setInterval(() => {
        const sparkle = document.createElement('div');
        sparkle.innerHTML = '✨';
        sparkle.style.position = 'absolute';
        sparkle.style.left = Math.random() * 100 + '%';
        sparkle.style.top = Math.random() * 100 + '%';
        sparkle.style.fontSize = '1rem';
        sparkle.style.animation = 'sparkleFloat 2s ease-in-out';
        sparkle.style.pointerEvents = 'none';
        
        photoFrame.appendChild(sparkle);
        
        setTimeout(() => {
            if (sparkle.parentNode) {
                sparkle.remove();
            }
        }, 2000);
    }, 3000);
}

// Keyboard shortcuts
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        closeLetter();
    }
    if (e.key === 'Enter') {
        openLetter();
    }
    if (e.key === ' ') {
        openGift();
    }
});

// Touch gestures cho mobile
let touchStartY = 0;
let touchEndY = 0;

document.addEventListener('touchstart', (e) => {
    touchStartY = e.changedTouches[0].screenY;
});

document.addEventListener('touchend', (e) => {
    touchEndY = e.changedTouches[0].screenY;
    handleSwipe();
});

function handleSwipe() {
    const swipeThreshold = 50;
    const diff = touchStartY - touchEndY;
    
    if (Math.abs(diff) > swipeThreshold) {
        if (diff > 0) {
            // Swipe up - open letter
            openLetter();
        } else {
            // Swipe down - open gift
            openGift();
        }
    }
}

// Khởi tạo khi trang load
document.addEventListener('DOMContentLoaded', () => {
    createStars();
    createHearts();
    addSparkleEffect();
    
    // Thêm hiệu ứng hover cho các element tương tác
    const interactiveElements = document.querySelectorAll('.letter-icon, .gift-section');
    interactiveElements.forEach(element => {
        element.addEventListener('mouseenter', () => {
            element.style.transform = 'scale(1.05)';
        });
        
        element.addEventListener('mouseleave', () => {
            element.style.transform = 'scale(1)';
        });
    });
    
    // Thêm hiệu ứng click ripple
    document.addEventListener('click', (e) => {
        if (e.target.closest('.letter-icon') || e.target.closest('.gift-section')) {
            createRippleEffect(e);
        }
    });
});

// Hiệu ứng ripple khi click
function createRippleEffect(e) {
    const ripple = document.createElement('div');
    ripple.style.position = 'absolute';
    ripple.style.borderRadius = '50%';
    ripple.style.background = 'rgba(255, 255, 255, 0.6)';
    ripple.style.transform = 'scale(0)';
    ripple.style.animation = 'ripple 0.6s linear';
    ripple.style.pointerEvents = 'none';
    
    const rect = e.target.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = e.clientX - rect.left - size / 2;
    const y = e.clientY - rect.top - size / 2;
    
    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';
    
    e.target.style.position = 'relative';
    e.target.appendChild(ripple);
    
    setTimeout(() => {
        if (ripple.parentNode) {
            ripple.remove();
        }
    }, 600);
}

// Thêm CSS cho ripple effect
const rippleStyle = document.createElement('style');
rippleStyle.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    @keyframes giftOpen {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }
`;
document.head.appendChild(rippleStyle);

// Music Function
function playMusic() {
    if (audio.paused) {
        audio.play().catch(e => {
            console.log('Audio play failed:', e);
            showMusicError();
        });
        updateMusicButton(true);
    } else {
        audio.pause();
        updateMusicButton(false);
    }
}

function updateMusicButton(isPlaying) {
    const musicBtn = document.querySelector('.btn-primary');
    const icon = musicBtn.querySelector('i');
    const text = musicBtn.querySelector('span') || musicBtn.lastChild;
    
    if (isPlaying) {
        icon.className = 'fas fa-pause';
        if (text.nodeType === Node.TEXT_NODE) {
            musicBtn.innerHTML = '<i class="fas fa-pause"></i> Tạm dừng';
        }
    } else {
        icon.className = 'fas fa-music';
        if (text.nodeType === Node.TEXT_NODE) {
            musicBtn.innerHTML = '<i class="fas fa-music"></i> Phát nhạc';
        }
    }
}

function showMusicError() {
    const errorDiv = document.createElement('div');
    errorDiv.innerHTML = '⚠️ Trình duyệt không hỗ trợ phát nhạc tự động';
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ff6b6b;
        color: white;
        padding: 10px 15px;
        border-radius: 10px;
        z-index: 10000;
        animation: slideIn 0.3s ease-out;
    `;
    
    document.body.appendChild(errorDiv);
    
    setTimeout(() => {
        errorDiv.remove();
    }, 3000);
}

// Sound Effect
function playSoundEffect() {
    // Create a simple beep sound
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
    oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
    
    gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + 0.2);
}

// Add Interactive Elements
function addInteractiveElements() {
    // Add hover effects to hearts
    const hearts = document.querySelectorAll('.heart');
    hearts.forEach(heart => {
        heart.addEventListener('mouseenter', function() {
            this.style.transform = 'rotate(45deg) scale(1.5)';
            this.style.transition = 'transform 0.3s ease';
        });
        
        heart.addEventListener('mouseleave', function() {
            this.style.transform = 'rotate(45deg) scale(1)';
        });
    });
    
    // Add click effects to wish items
    const wishItems = document.querySelectorAll('.wish-item');
    wishItems.forEach(item => {
        item.addEventListener('click', function() {
            this.style.transform = 'translateY(-5px) scale(1.05)';
            setTimeout(() => {
                this.style.transform = 'translateY(0) scale(1)';
            }, 200);
            
            // Create sparkle effect
            createSparkle(this);
        });
    });
}

// Sparkle Effect
function createSparkle(element) {
    const rect = element.getBoundingClientRect();
    const sparkleCount = 5;
    
    for (let i = 0; i < sparkleCount; i++) {
        const sparkle = document.createElement('div');
        sparkle.style.cssText = `
            position: fixed;
            left: ${rect.left + Math.random() * rect.width}px;
            top: ${rect.top + Math.random() * rect.height}px;
            width: 4px;
            height: 4px;
            background: #ffd700;
            border-radius: 50%;
            pointer-events: none;
            z-index: 1000;
            animation: sparkle 1s ease-out forwards;
        `;
        
        document.body.appendChild(sparkle);
        
        setTimeout(() => {
            if (sparkle.parentNode) {
                sparkle.parentNode.removeChild(sparkle);
            }
        }, 1000);
    }
    
    // Add sparkle animation
    if (!document.querySelector('#sparkle-animation')) {
        const style = document.createElement('style');
        style.id = 'sparkle-animation';
        style.textContent = `
            @keyframes sparkle {
                0% {
                    transform: scale(0) rotate(0deg);
                    opacity: 1;
                }
                50% {
                    transform: scale(1) rotate(180deg);
                    opacity: 1;
                }
                100% {
                    transform: scale(0) rotate(360deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }
}

// Parallax Effect for Background
window.addEventListener('scroll', function() {
    const scrolled = window.pageYOffset;
    const hearts = document.querySelectorAll('.heart');
    
    hearts.forEach((heart, index) => {
        const speed = 0.5 + (index * 0.1);
        heart.style.transform = `rotate(45deg) translateY(${scrolled * speed}px)`;
    });
});

// Add CSS animations for better performance
const additionalStyles = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    .surprise-message {
        backdrop-filter: blur(5px);
    }
    
    .gift-section:active {
        transform: scale(0.95);
    }
    
    .btn:active {
        transform: translateY(-1px);
    }
`;

const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);

// Performance optimization
let animationFrameId;
function optimizeAnimations() {
    const elements = document.querySelectorAll('.heart, .flame, .candle');
    elements.forEach(element => {
        element.style.willChange = 'transform';
    });
}

// Call optimization after page load
setTimeout(optimizeAnimations, 1000);

// Cleanup function
function cleanup() {
    if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
    }
    if (audio) {
        audio.pause();
        audio.currentTime = 0;
    }
}

// Cleanup on page unload
window.addEventListener('beforeunload', cleanup);

// Sticker động: lắc nhẹ sticker
function animateSticker() {
    const sticker = document.querySelector('.sticker');
    if (!sticker) return;
    let angle = 0;
    setInterval(() => {
        angle = Math.sin(Date.now() / 600) * 6;
        sticker.style.transform = `rotate(${angle}deg)`;
    }, 60);
}

// Bóng bay chuyển động nhẹ nhàng
function animateBalloons() {
    const balloons = document.querySelectorAll('.balloon');
    balloons.forEach((balloon, i) => {
        let base = Math.random() * 100;
        setInterval(() => {
            const t = Date.now() / 1000 + i * 0.7;
            const x = Math.sin(t) * 6;
            const y = Math.cos(t * 0.7) * 8;
            balloon.style.transform = `translate(${x}px, ${y}px)`;
        }, 60);
    });
}

// Nhạc nền sinh nhật
const music = document.getElementById('birthday-music');

function toggleMusic() {
    if (!music) return;
    if (music.paused) {
        music.play();
        isMusicPlaying = true;
        musicToggle.classList.add('active');
    } else {
        music.pause();
        isMusicPlaying = false;
        musicToggle.classList.remove('active');
    }
}

// Tự động bật nhạc khi người dùng tương tác đầu tiên
window.addEventListener('click', function autoPlayMusic() {
    if (!isMusicPlaying && music) {
        music.play();
        isMusicPlaying = true;
        musicToggle.classList.add('active');
    }
    window.removeEventListener('click', autoPlayMusic);
});

// Hiệu ứng mưa confetti nền liên tục
setInterval(() => {
    createConfetti(5);
}, 1800);

// Hiệu ứng trái tim bay tự động trên nền
function autoFloatingHearts() {
    const heartsBg = document.body;
    const heart = document.createElement('div');
    heart.className = 'auto-heart';
    heart.innerHTML = '💖';
    heart.style.position = 'fixed';
    heart.style.left = Math.random() * 100 + 'vw';
    heart.style.bottom = '-40px';
    heart.style.fontSize = (Math.random() * 24 + 24) + 'px';
    heart.style.opacity = Math.random() * 0.5 + 0.5;
    heart.style.pointerEvents = 'none';
    heart.style.zIndex = 2;
    heart.style.transition = 'transform 4s linear, opacity 4s linear';
    document.body.appendChild(heart);
    setTimeout(() => {
        heart.style.transform = `translateY(-110vh) scale(${Math.random() * 0.5 + 0.8})`;
        heart.style.opacity = 0;
    }, 100);
    setTimeout(() => {
        heart.remove();
    }, 4200);
}
setInterval(autoFloatingHearts, 1200); 