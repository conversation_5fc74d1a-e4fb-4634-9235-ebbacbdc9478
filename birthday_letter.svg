<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <!-- Gradient definitions -->
  <defs>
    <linearGradient id="envelopeGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E6F3FF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#B3D9FF;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="flapGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#B3D9FF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#87CEEB;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Envelope body -->
  <rect x="50" y="80" width="100" height="70" rx="8" ry="8" fill="url(#envelopeGrad)" stroke="none"/>

  <!-- Envelope flap -->
  <polygon points="50,80 100,115 150,80" fill="url(#flapGrad)" stroke="none"/>

  <!-- Heart decoration on envelope -->
  <path d="M100,95 C96,91 88,91 88,98 C88,102 100,112 100,112 C100,112 112,102 112,98 C112,91 104,91 100,95 Z" fill="#4169E1"/>

  <!-- Sparkles around the envelope -->
  <circle cx="75" cy="65" r="2" fill="#FFD700" opacity="0.8"/>
  <circle cx="125" cy="65" r="2" fill="#FFD700" opacity="0.8"/>
  <circle cx="160" cy="100" r="1.5" fill="#FFD700" opacity="0.8"/>
  <circle cx="40" cy="100" r="1.5" fill="#FFD700" opacity="0.8"/>
  <circle cx="75" cy="165" r="2" fill="#FFD700" opacity="0.8"/>
  <circle cx="125" cy="165" r="2" fill="#FFD700" opacity="0.8"/>

  <!-- Small hearts floating around -->
  <path d="M70,50 C68,48 66,48 66,50 C66,52 70,55 70,55 C70,55 74,52 74,50 C74,48 72,48 70,50 Z" fill="#6495ED" opacity="0.7"/>
  <path d="M130,50 C128,48 126,48 126,50 C126,52 130,55 130,55 C130,55 134,52 134,50 C134,48 132,48 130,50 Z" fill="#6495ED" opacity="0.7"/>
  <path d="M165,130 C163,128 161,128 161,130 C161,132 165,135 165,135 C165,135 169,132 169,130 C169,128 167,128 165,130 Z" fill="#6495ED" opacity="0.7"/>
  <path d="M35,130 C33,128 31,128 31,130 C31,132 35,135 35,135 C35,135 39,132 39,130 C39,128 37,128 35,130 Z" fill="#6495ED" opacity="0.7"/>
</svg>
